package com.example.chat;

import javax.swing.*;
import javax.swing.border.*;
import javax.swing.text.*;
import javax.swing.plaf.basic.BasicScrollBarUI;
import java.awt.*;
import java.awt.event.*;
import java.awt.geom.RoundRectangle2D;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import javax.swing.Timer;

class CopilotStyleChat extends JFrame {
    private JPanel chatPanel;
    private JScrollPane scrollPane;
    private JTextArea inputArea;
    private JButton sendButton;
    private JLabel typingIndicator;
    private List<ChatMessage> messages;

    // GitHub Copilot 风格配色
    private static final Color BG_COLOR = new Color(13, 17, 23);
    private static final Color SECONDARY_BG = new Color(22, 27, 34);
    private static final Color TERTIARY_BG = new Color(33, 38, 45);
    private static final Color USER_MSG_BG = new Color(56, 139, 253);
    private static final Color AI_MSG_BG = new Color(33, 38, 45);
    private static final Color TEXT_PRIMARY = new Color(201, 209, 217);
    private static final Color TEXT_SECONDARY = new Color(139, 148, 158);
    private static final Color BORDER_COLOR = new Color(48, 54, 61);
    private static final Color HOVER_COLOR = new Color(48, 54, 61);
    private static final Color INPUT_BG = new Color(13, 17, 23);
    private static final Color ACCENT_COLOR = new Color(136, 192, 208);

    // 字体
    private static final Font TITLE_FONT = new Font("Segoe UI", Font.BOLD, 16);
    private static final Font MESSAGE_FONT = new Font("Segoe UI", Font.PLAIN, 14);
    private static final Font TIME_FONT = new Font("Segoe UI", Font.PLAIN, 11);
    private static final Font INPUT_FONT = new Font("Segoe UI", Font.PLAIN, 14);

    public CopilotStyleChat() {
        messages = new ArrayList<>();
        initializeUI();
    }

    private void initializeUI() {
        setTitle("GitHub Copilot Chat");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(420, 720);
        setLocationRelativeTo(null);
        setUndecorated(true);

        // 设置圆角窗口
        setShape(new RoundRectangle2D.Double(0, 0, getWidth(), getHeight(), 20, 20));

        // 主面板
        JPanel mainPanel = new JPanel(new BorderLayout()) {
            @Override
            protected void paintComponent(Graphics g) {
                super.paintComponent(g);
                Graphics2D g2 = (Graphics2D) g;
                g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                g2.setColor(BG_COLOR);
                g2.fillRoundRect(0, 0, getWidth(), getHeight(), 20, 20);
            }
        };
        mainPanel.setBackground(BG_COLOR);
        mainPanel.setBorder(BorderFactory.createEmptyBorder(0, 0, 0, 0));

        // 顶部标题栏
        JPanel headerPanel = createHeaderPanel();
        mainPanel.add(headerPanel, BorderLayout.NORTH);

        // 聊天区域容器
        JPanel chatContainer = new JPanel(new BorderLayout());
        chatContainer.setBackground(BG_COLOR);
        chatContainer.setBorder(BorderFactory.createEmptyBorder(0, 15, 0, 15));

        // 聊天面板
        chatPanel = new JPanel();
        chatPanel.setLayout(new BoxLayout(chatPanel, BoxLayout.Y_AXIS));
        chatPanel.setBackground(BG_COLOR);
        chatPanel.setBorder(BorderFactory.createEmptyBorder(10, 5, 10, 5));

        scrollPane = new JScrollPane(chatPanel);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);
        scrollPane.setBorder(null);
        scrollPane.getViewport().setBackground(BG_COLOR);

        // 自定义滚动条
        JScrollBar verticalBar = scrollPane.getVerticalScrollBar();
        verticalBar.setUI(new CopilotScrollBarUI());
        verticalBar.setPreferredSize(new Dimension(8, 0));

        chatContainer.add(scrollPane, BorderLayout.CENTER);

        // 输入提示标签
        typingIndicator = new JLabel("AI is typing...");
        typingIndicator.setFont(TIME_FONT);
        typingIndicator.setForeground(TEXT_SECONDARY);
        typingIndicator.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
        typingIndicator.setVisible(false);
        chatContainer.add(typingIndicator, BorderLayout.SOUTH);

        mainPanel.add(chatContainer, BorderLayout.CENTER);

        // 输入区域
        JPanel inputPanel = createInputPanel();
        mainPanel.add(inputPanel, BorderLayout.SOUTH);

        setContentPane(mainPanel);

        // 添加窗口拖动功能
        addWindowDragFunctionality(headerPanel);

        // 添加欢迎消息
        addMessage(new ChatMessage("Copilot", "👋 Hi! I'm GitHub Copilot. How can I help you today?", false));
    }

    private JPanel createHeaderPanel() {
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBackground(SECONDARY_BG);
        headerPanel.setPreferredSize(new Dimension(0, 50));
        headerPanel.setBorder(BorderFactory.createMatteBorder(0, 0, 1, 0, BORDER_COLOR));

        // 左侧标题部分
        JPanel leftPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 15, 12));
        leftPanel.setBackground(SECONDARY_BG);

        // Copilot 图标（使用彩色圆点代替）
        JPanel iconPanel = new JPanel() {
            @Override
            protected void paintComponent(Graphics g) {
                super.paintComponent(g);
                Graphics2D g2 = (Graphics2D) g;
                g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

                // 绘制渐变圆形图标
                GradientPaint gradient = new GradientPaint(0, 0, new Color(136, 192, 208),
                        25, 25, new Color(94, 158, 214));
                g2.setPaint(gradient);
                g2.fillOval(0, 0, 25, 25);
            }
        };
        iconPanel.setPreferredSize(new Dimension(25, 25));
        iconPanel.setBackground(SECONDARY_BG);

        JLabel titleLabel = new JLabel("GitHub Copilot");
        titleLabel.setFont(TITLE_FONT);
        titleLabel.setForeground(TEXT_PRIMARY);

        leftPanel.add(iconPanel);
        leftPanel.add(titleLabel);

        // 右侧控制按钮
        JPanel rightPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 10));
        rightPanel.setBackground(SECONDARY_BG);

        // 最小化按钮
        JButton minimizeBtn = createControlButton("－");
        minimizeBtn.addActionListener(e -> setState(Frame.ICONIFIED));

        // 关闭按钮
        JButton closeBtn = createControlButton("×");
        closeBtn.addActionListener(e -> System.exit(0));

        rightPanel.add(minimizeBtn);
        rightPanel.add(closeBtn);

        headerPanel.add(leftPanel, BorderLayout.WEST);
        headerPanel.add(rightPanel, BorderLayout.EAST);

        return headerPanel;
    }

    private JButton createControlButton(String text) {
        JButton button = new JButton(text) {
            @Override
            protected void paintComponent(Graphics g) {
                Graphics2D g2 = (Graphics2D) g;
                g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

                if (getModel().isRollover()) {
                    g2.setColor(HOVER_COLOR);
                    g2.fillRoundRect(0, 0, getWidth(), getHeight(), 5, 5);
                }

                g2.setColor(TEXT_SECONDARY);
                FontMetrics fm = g2.getFontMetrics();
                int x = (getWidth() - fm.stringWidth(text)) / 2;
                int y = (getHeight() + fm.getAscent() - fm.getDescent()) / 2;
                g2.drawString(text, x, y);
            }
        };
        button.setFont(new Font("Segoe UI", Font.PLAIN, 16));
        button.setPreferredSize(new Dimension(30, 30));
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setContentAreaFilled(false);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        return button;
    }

    private JPanel createInputPanel() {
        JPanel inputPanel = new JPanel(new BorderLayout());
        inputPanel.setBackground(BG_COLOR);
        inputPanel.setBorder(BorderFactory.createEmptyBorder(10, 15, 15, 15));

        // 输入区域容器
        JPanel inputContainer = new JPanel(new BorderLayout(10, 0));
        inputContainer.setBackground(INPUT_BG);
        inputContainer.setBorder(BorderFactory.createCompoundBorder(
                new RoundedBorder(BORDER_COLOR, 12),
                BorderFactory.createEmptyBorder(5, 5, 5, 5)
        ));

        // 输入框
        inputArea = new JTextArea(2, 20);
        inputArea.setFont(INPUT_FONT);
        inputArea.setLineWrap(true);
        inputArea.setWrapStyleWord(true);
        inputArea.setBackground(INPUT_BG);
        inputArea.setForeground(TEXT_PRIMARY);
        inputArea.setCaretColor(ACCENT_COLOR);
        inputArea.setBorder(BorderFactory.createEmptyBorder(8, 10, 8, 10));

        // 占位符文本
        inputArea.setText("Ask Copilot...");
        inputArea.setForeground(TEXT_SECONDARY);

        inputArea.addFocusListener(new FocusAdapter() {
            @Override
            public void focusGained(FocusEvent e) {
                if (inputArea.getText().equals("Ask Copilot...")) {
                    inputArea.setText("");
                    inputArea.setForeground(TEXT_PRIMARY);
                }
            }

            @Override
            public void focusLost(FocusEvent e) {
                if (inputArea.getText().isEmpty()) {
                    inputArea.setText("Ask Copilot...");
                    inputArea.setForeground(TEXT_SECONDARY);
                }
            }
        });

        // Enter 发送消息
        inputArea.addKeyListener(new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                if (e.getKeyCode() == KeyEvent.VK_ENTER && !e.isShiftDown()) {
                    e.consume();
                    sendMessage();
                }
            }
        });

        JScrollPane inputScrollPane = new JScrollPane(inputArea);
        inputScrollPane.setBorder(null);
        inputScrollPane.getViewport().setBackground(INPUT_BG);
        inputScrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);

        // 发送按钮
        sendButton = new JButton() {
            @Override
            protected void paintComponent(Graphics g) {
                Graphics2D g2 = (Graphics2D) g;
                g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

                // 绘制圆形背景
                if (getModel().isRollover() || getModel().isPressed()) {
                    g2.setColor(USER_MSG_BG.darker());
                } else {
                    g2.setColor(USER_MSG_BG);
                }
                g2.fillOval(0, 0, getWidth(), getHeight());

                // 绘制发送图标
                g2.setColor(Color.WHITE);
                int[] xPoints = {12, 24, 12};
                int[] yPoints = {10, 18, 26};
                g2.fillPolygon(xPoints, yPoints, 3);
            }
        };
        sendButton.setPreferredSize(new Dimension(36, 36));
        sendButton.setFocusPainted(false);
        sendButton.setBorderPainted(false);
        sendButton.setContentAreaFilled(false);
        sendButton.setCursor(new Cursor(Cursor.HAND_CURSOR));
        sendButton.addActionListener(e -> sendMessage());

        inputContainer.add(inputScrollPane, BorderLayout.CENTER);
        inputContainer.add(sendButton, BorderLayout.EAST);

        inputPanel.add(inputContainer);

        return inputPanel;
    }

    private void sendMessage() {
        String text = inputArea.getText().trim();
        if (!text.isEmpty() && !text.equals("Ask Copilot...")) {
            // 添加用户消息
            addMessage(new ChatMessage("You", text, true));
            inputArea.setText("");
            inputArea.setForeground(TEXT_PRIMARY);

            // 显示输入指示器
            typingIndicator.setVisible(true);

            // 模拟AI回复
            Timer timer = new Timer(1500, e -> {
                typingIndicator.setVisible(false);
                String aiResponse = generateAIResponse(text);
                addMessage(new ChatMessage("Copilot", aiResponse, false));
            });
            timer.setRepeats(false);
            timer.start();
        }
    }

    private void addMessage(ChatMessage message) {
        messages.add(message);

        JPanel messagePanel = createMessagePanel(message);

        // 添加淡入动画效果
        messagePanel.setAlpha(0f);
        chatPanel.add(messagePanel);
        chatPanel.add(Box.createVerticalStrut(12));

        // 动画淡入
        Timer fadeIn = new Timer(20, null);
        fadeIn.addActionListener(new ActionListener() {
            float alpha = 0f;

            @Override
            public void actionPerformed(ActionEvent e) {
                alpha += 0.05f;
                if (alpha >= 1f) {
                    alpha = 1f;
                    fadeIn.stop();
                }
                messagePanel.setAlpha(alpha);
                messagePanel.repaint();
            }
        });
        fadeIn.start();

        // 自动滚动到底部
        SwingUtilities.invokeLater(() -> {
            scrollPane.getVerticalScrollBar().setValue(
                    scrollPane.getVerticalScrollBar().getMaximum()
            );
        });

        chatPanel.revalidate();
        chatPanel.repaint();
    }

    private JPanel createMessagePanel(ChatMessage message) {
        FadePanel panel = new FadePanel();
        panel.setLayout(new BorderLayout());
        panel.setBackground(BG_COLOR);
        panel.setMaximumSize(new Dimension(Integer.MAX_VALUE, Integer.MAX_VALUE));

        // 消息容器
        JPanel messageContainer = new JPanel();
        messageContainer.setLayout(new BorderLayout());
        messageContainer.setBackground(message.isUser ? USER_MSG_BG : AI_MSG_BG);
        messageContainer.setBorder(new RoundedBorder(
                message.isUser ? USER_MSG_BG : BORDER_COLOR,
                12
        ));

        // 内容面板
        JPanel contentPanel = new JPanel();
        contentPanel.setLayout(new BoxLayout(contentPanel, BoxLayout.Y_AXIS));
        contentPanel.setBackground(message.isUser ? USER_MSG_BG : AI_MSG_BG);
        contentPanel.setBorder(BorderFactory.createEmptyBorder(12, 16, 12, 16));

        // 消息内容
        JTextPane contentPane = new JTextPane();
        contentPane.setText(message.content);
        contentPane.setFont(MESSAGE_FONT);
        contentPane.setForeground(message.isUser ? Color.WHITE : TEXT_PRIMARY);
        contentPane.setBackground(message.isUser ? USER_MSG_BG : AI_MSG_BG);
        contentPane.setEditable(false);
        contentPane.setBorder(null);

        // 时间戳
        JLabel timeLabel = new JLabel(message.timestamp);
        timeLabel.setFont(TIME_FONT);
        timeLabel.setForeground(message.isUser ?
                new Color(255, 255, 255, 180) : TEXT_SECONDARY);
        timeLabel.setBorder(BorderFactory.createEmptyBorder(5, 0, 0, 0));

        contentPanel.add(contentPane);
        contentPanel.add(timeLabel);

        messageContainer.add(contentPanel);

        // 根据消息类型调整对齐和边距
        if (message.isUser) {
            panel.add(Box.createHorizontalStrut(80), BorderLayout.WEST);
            panel.add(messageContainer, BorderLayout.CENTER);
            panel.add(Box.createHorizontalStrut(5), BorderLayout.EAST);
        } else {
            panel.add(Box.createHorizontalStrut(5), BorderLayout.WEST);
            panel.add(messageContainer, BorderLayout.CENTER);
            panel.add(Box.createHorizontalStrut(80), BorderLayout.EAST);
        }

        return panel;
    }

    private String generateAIResponse(String userInput) {
        String[] responses = {
                "I can help you with that! Based on my analysis, here's what I suggest:\n\n" +
                        "1. First, let's break down the problem\n" +
                        "2. Consider the different approaches\n" +
                        "3. Implement the most suitable solution\n\n" +
                        "Would you like me to elaborate on any of these points?",

                "That's an interesting question! Here's my perspective:\n\n" +
                        "The key insight here is to understand the underlying pattern. " +
                        "Once we identify that, the solution becomes much clearer.\n\n" +
                        "Shall I provide a code example?",

                "Great question! Let me help you understand this better.\n\n" +
                        "From what you've described, it seems like you're looking for a solution that balances " +
                        "both performance and maintainability. Here's what I recommend...",

                "I understand what you're trying to achieve. Here's a comprehensive approach:\n\n" +
                        "• Start with the core functionality\n" +
                        "• Build incrementally\n" +
                        "• Test each component thoroughly\n\n" +
                        "This methodology has proven effective in similar scenarios."
        };

        return responses[(int) (Math.random() * responses.length)];
    }

    // 添加窗口拖动功能
    private void addWindowDragFunctionality(JPanel panel) {
        final Point[] mouseDownCompCoords = new Point[1];

        panel.addMouseListener(new MouseAdapter() {
            public void mousePressed(MouseEvent e) {
                mouseDownCompCoords[0] = e.getPoint();
            }
        });

        panel.addMouseMotionListener(new MouseMotionAdapter() {
            public void mouseDragged(MouseEvent e) {
                Point currCoords = e.getLocationOnScreen();
                setLocation(currCoords.x - mouseDownCompCoords[0].x,
                        currCoords.y - mouseDownCompCoords[0].y);
            }
        });
    }

    // 聊天消息类
    private static class ChatMessage {
        String sender;
        String content;
        boolean isUser;
        String timestamp;

        ChatMessage(String sender, String content, boolean isUser) {
            this.sender = sender;
            this.content = content;
            this.isUser = isUser;
            this.timestamp = LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm"));
        }
    }

    // 自定义滚动条UI
    private static class CopilotScrollBarUI extends BasicScrollBarUI {
        @Override
        protected void configureScrollBarColors() {
            this.thumbColor = new Color(139, 148, 158, 100);
            this.trackColor = BG_COLOR;
        }

        @Override
        protected JButton createDecreaseButton(int orientation) {
            return createZeroButton();
        }

        @Override
        protected JButton createIncreaseButton(int orientation) {
            return createZeroButton();
        }

        private JButton createZeroButton() {
            JButton button = new JButton();
            button.setPreferredSize(new Dimension(0, 0));
            return button;
        }

        @Override
        protected void paintThumb(Graphics g, JComponent c, Rectangle thumbBounds) {
            Graphics2D g2 = (Graphics2D) g.create();
            g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2.setPaint(new Color(139, 148, 158, 120));
            g2.fillRoundRect(thumbBounds.x + 2, thumbBounds.y,
                    thumbBounds.width - 4, thumbBounds.height, 4, 4);
            g2.dispose();
        }

        @Override
        protected void paintTrack(Graphics g, JComponent c, Rectangle trackBounds) {
            // 不绘制轨道
        }
    }

    // 圆角边框
    private static class RoundedBorder implements Border {
        private Color color;
        private int radius;

        RoundedBorder(Color color, int radius) {
            this.color = color;
            this.radius = radius;
        }

        @Override
        public void paintBorder(Component c, Graphics g, int x, int y, int width, int height) {
            Graphics2D g2 = (Graphics2D) g.create();
            g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2.setColor(color);
            g2.drawRoundRect(x, y, width - 1, height - 1, radius, radius);
            g2.dispose();
        }

        @Override
        public Insets getBorderInsets(Component c) {
            return new Insets(1, 1, 1, 1);
        }

        @Override
        public boolean isBorderOpaque() {
            return false;
        }
    }

    // 支持透明度的面板
    private static class FadePanel extends JPanel {
        private float alpha = 1f;

        public void setAlpha(float alpha) {
            this.alpha = alpha;
        }

        @Override
        protected void paintComponent(Graphics g) {
            Graphics2D g2 = (Graphics2D) g.create();
            g2.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alpha));
            super.paintComponent(g2);
            g2.dispose();
        }

        @Override
        protected void paintChildren(Graphics g) {
            Graphics2D g2 = (Graphics2D) g.create();
            g2.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alpha));
            super.paintChildren(g2);
            g2.dispose();
        }
    }

    public static void main(String[] args) {
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
        } catch (Exception e) {
            e.printStackTrace();
        }

        SwingUtilities.invokeLater(() -> {
            CopilotStyleChat chat = new CopilotStyleChat();
            chat.setVisible(true);
        });
    }
}